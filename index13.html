<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Podcy - Home</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Public Sans', -apple-system, BlinkMacSystemFont, sans-serif;
            background-color: #ffffff;
        }

        .podcy-home {
            width: 4.28rem;
            height: 9.26rem;
            background-color: #ffffff;
            position: relative;
        }

        .group-147 {
            position: absolute;
            left: 3.48rem;
            top: 0.48rem;
            width: 0.48rem;
            height: 0.48rem;
        }

        .group-146 {
            position: absolute;
            left: 0rem;
            top: 0rem;
            width: 0.48rem;
            height: 0.48rem;
        }

        .rectangle-1563 {
            position: absolute;
            left: 0rem;
            top: 0rem;
            width: 0.48rem;
            height: 0.48rem;
            background-color: rgba(31, 31, 31, 0.1);
            border-radius: 0.24rem;
        }

        .frame {
            position: absolute;
            left: 0.13rem;
            top: 0.13rem;
            width: 0.21rem;
            height: 0.21rem;
        }

        .bell-simple-fill {
            position: absolute;
            left: 0rem;
            top: 0rem;
            width: 0.21rem;
            height: 0.21rem;
            background-image: url('images/bell-simple-fill.png');
            background-size: contain;
            background-repeat: no-repeat;
            background-position: center;
        }

        .rectangle-1564 {
            position: absolute;
            left: 0.34rem;
            top: 0.02rem;
            width: 0.12rem;
            height: 0.12rem;
            background-color: #ff5757;
            border-radius: 0.06rem;
        }

        .rectangle-1569 {
            position: absolute;
            left: 0.32rem;
            top: 1.28rem;
            width: 3.64rem;
            height: 0.64rem;
            background-color: rgba(31, 31, 31, 0.08);
            border-radius: 0.32rem;
        }

        .group-167 {
            position: absolute;
            left: 0.52rem;
            top: 1.48rem;
            width: 0.24rem;
            height: 0.24rem;
        }

        .rectangle-2982 {
            position: absolute;
            left: 0rem;
            top: 0rem;
            width: 0.24rem;
            height: 0.24rem;
            background-image: url('images/group-167.png');
            background-size: contain;
            background-repeat: no-repeat;
            background-position: center;
        }

        .search-the-podcast-here {
            position: absolute;
            left: 0.88rem;
            top: 1.5rem;
            height: 0.19rem;
            font-family: 'Public Sans', -apple-system, BlinkMacSystemFont, sans-serif;
            font-weight: 500;
            font-size: 0.16rem;
            line-height: 0.188rem;
            color: rgba(31, 31, 31, 0.5);
            text-align: left;
        }

        .promoted-podcasts {
            position: absolute;
            left: 0.32rem;
            top: 2.24rem;
            height: 0.24rem;
            font-family: 'Public Sans', -apple-system, BlinkMacSystemFont, sans-serif;
            font-weight: 700;
            font-size: 0.2rem;
            line-height: 0.235rem;
            color: rgb(31, 31, 31);
            text-align: left;
        }
    </style>
</head>
<body>
    <div class="podcy-home">
        <div class="group-147">
            <div class="group-146">
                <div class="rectangle-1563"></div>
                <div class="frame">
                    <div class="bell-simple-fill"></div>
                </div>
            </div>
            <div class="rectangle-1564"></div>
        </div>
        <div class="rectangle-1569"></div>
        <div class="group-167">
            <div class="rectangle-2982"></div>
        </div>
        <div class="search-the-podcast-here">Search the podcast here...</div>
        <div class="promoted-podcasts">Promoted Podcasts</div>
    </div>

    <script>
        (function () {
            const designWidth = 428; // 设计稿宽度
            const baseRem = 100;      // 设定 1rem = 100px，方便换算

            function setRootFontSize() {
                const html = document.documentElement;
                const clientWidth = html.clientWidth;

                // 让页面宽度和设计稿成等比缩放
                html.style.fontSize = (clientWidth / designWidth) * baseRem + 'px';
            }

            setRootFontSize();
            window.addEventListener('resize', setRootFontSize);
        })();
    </script>
</body>
</html>
